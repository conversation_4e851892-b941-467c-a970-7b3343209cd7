"use client";

import { useState, useEffect, useCallback } from "react";
import { useParams } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import { useContracts } from "@/hooks/useContracts";
import { useLayout } from "@/hooks/useLayout";
import { CanRead, RBACWrapper } from "@/components/common/rbac/RBACWrapper";
import { DEFAULT_ENTITIES, PERMISSION_ACTIONS } from "@/lib/rbac";
import { Listing } from "@/layouts/dashboard/details";
import { Button } from "@/components/common/ui/button";
import { type Contract } from "@/components/common/types";
import { useRouter } from "next/navigation";
import type {
  Contract as ApiContract,
  CreateContract,
} from "@/lib/api/validators/schemas/contract";
import type { DataType } from "@/layouts/dashboard/details";
import type { InsightsWidget } from "@/layouts/dashboard/details/advanced";
import { ContractCreateDialog } from "./create-dialog";
import { ContractEditDialog } from "./details/edit-dialog";
import { ContractDeleteDialog } from "./details/delete-dialog";
import { ContractHeader } from "./header";
import { ContractStatistics } from "./statistics";
import { ContractTable } from "./table";
import { FileText, DollarSign, Calendar, TrendingUp } from "lucide-react";
import { LottieIconPlayer, LottieIconLib } from "@/components/common/lottie";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";

// Main Container Component
function ContractsContainer() {
  const { slug } = useParams();
  const router = useRouter();
  const { personalizedRoute } = useAuth();
  const { isAdvanced } = useLayout();

  const {
    contracts,
    statistics,
    isLoading,
    isSearching,
    error,
    searchTerm,
    setSearchTerm,
    searchContracts,
    createContract,
    updateContract,
    deleteContract,
    clearError,
    initializeContracts,
  } = useContracts();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedContract, setSelectedContract] = useState<Contract | null>(
    null
  );
  const [basicViewMode, setBasicViewMode] = useState<"pages" | "flow">("pages");
  const [advancedViewMode, setAdvancedViewMode] = useState<
    "table" | "grid" | "list" | "flow"
  >("table");
  const [controlsViewMode, setControlsViewMode] = useState<"table" | "cards">(
    "table"
  );
  const [categoryFilter, setCategoryFilter] = useState<string>("all");

  // Combined loading state for table
  const isTableLoading = isLoading || isSearching;

  // Handle search functionality with immediate UI update
  const handleSearch = useCallback(
    (query: string) => {
      // Update search term immediately for UI responsiveness
      setSearchTerm(query);
    },
    [setSearchTerm]
  );

  // Handle refresh functionality
  const handleRefresh = useCallback(() => {
    // Clear search term and refresh data
    setSearchTerm("");
    initializeContracts();
  }, [setSearchTerm, initializeContracts]);

  useEffect(() => {
    initializeContracts();
  }, [slug, initializeContracts]);

  useEffect(() => {
    if (error) {
      console.error("Contract error:", error);
    }
  }, [error]);

  useEffect(() => {
    return () => {
      clearError();
    };
  }, [clearError]);

  // Pagination is handled internally by the TableView component

  function handleSearchCommit(value: string) {
    searchContracts({ query: value });
  }

  const handleOpenContract = (contract: Contract) => {
    if (!slug || !contract || !contract?.id) return;
    router.push(`/${slug}/contracts/${contract?.id}`);
  };

  const handleCreateContract = async (formData: CreateContract) => {
    try {
      await createContract(formData);
      setIsCreateDialogOpen(false);
    } catch (error) {
      console.error("Failed to create contract:", error);
    }
  };

  // Contract operations are now handled through context menu actions
  // handleUpdateContract and handleDeleteContract removed as they're replaced by contextMenuActions

  // Helper functions for dialog management
  const handleEditContractDialog = (contract: Contract) => {
    // Store UI contract for editing (dialog will handle API conversion on submit)
    setSelectedContract(contract);
    setIsEditDialogOpen(true);
  };

  const handleDeleteContractDialog = (contract: Contract) => {
    // Store UI contract for deletion (will convert to API format when needed)
    setSelectedContract(contract);
    setIsDeleteDialogOpen(true);
  };

  const handleEditSubmit = async (data: Partial<ApiContract>) => {
    if (selectedContract) {
      try {
        await updateContract(selectedContract.id, data);
        setIsEditDialogOpen(false);
        setSelectedContract(null);
      } catch (error) {
        console.error("Failed to update contract:", error);
      }
    }
  };

  const handleDeleteConfirm = async () => {
    if (selectedContract) {
      try {
        await deleteContract(selectedContract.id);
        setIsDeleteDialogOpen(false);
        setSelectedContract(null);
      } catch (error) {
        console.error("Failed to delete contract:", error);
      }
    }
  };

  // Function to determine data type for dynamic icons (advanced layout)
  const getDataType = (item: Contract, columnKey: string): DataType => {
    if (columnKey === "title") {
      return "document";
    }
    // Reference item to avoid unused parameter warning
    return item ? "default" : "default";
  };

  // Custom card renderer for contracts
  const renderContractCard = (contract: Contract, index: number) => {
    return (
      <Card className="shadow-sm hover:shadow-lg transition-all duration-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium">
            {contract.title || `Contract ${index + 1}`}
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2">
            <div className="flex justify-between items-center text-sm">
              <span className="text-muted-foreground">Client:</span>
              <span className="font-medium">{contract.clientName}</span>
            </div>
            <div className="flex justify-between items-center text-sm">
              <span className="text-muted-foreground">Status:</span>
              <span className="font-medium capitalize">{contract.status}</span>
            </div>
            <div className="flex justify-between items-center text-sm">
              <span className="text-muted-foreground">Value:</span>
              <span className="font-medium">
                ${contract.contractValue?.toLocaleString() || "0"}
              </span>
            </div>
          </div>
        </CardContent>
        <CardFooter className="pt-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleOpenContract(contract)}
            className="w-full"
          >
            <LottieIconPlayer
              icon={LottieIconLib.view}
              size={16}
              className="mr-2"
            />
            Open
          </Button>
        </CardFooter>
      </Card>
    );
  };

  // Quick filters for advanced layout
  const quickFilters = [
    {
      label: "All Contracts",
      value: "all",
      count: contracts.length,
      active: searchTerm === "",
      onClick: () => setSearchTerm(""),
    },
    {
      label: "Active",
      value: "active",
      count: contracts.filter(
        (contract: Contract) => contract.status === "active"
      ).length,
      active: false,
      onClick: () => setSearchTerm("active"),
    },
    {
      label: "Draft",
      value: "draft",
      count: contracts.filter(
        (contract: Contract) => contract.status === "draft"
      ).length,
      active: false,
      onClick: () => setSearchTerm("draft"),
    },
    {
      label: "Completed",
      value: "completed",
      count: contracts.filter(
        (contract: Contract) => contract.status === "completed"
      ).length,
      active: false,
      onClick: () => setSearchTerm("completed"),
    },
  ];

  // Insights widgets for advanced layout
  const insightsWidgets: InsightsWidget[] = [
    {
      id: "total-contracts",
      title: "Total Contracts",
      value: statistics?.totalContracts || 0,
      description: "All contracts in the system",
      icon: <FileText size={16} className="text-blue-500" />,
      trend: {
        value: 8,
        isPositive: true,
      },
    },
    {
      id: "active-contracts",
      title: "Active Contracts",
      value: statistics?.activeContracts || 0,
      description: "Currently active contracts",
      icon: <TrendingUp size={16} className="text-green-500" />,
      trend: {
        value: 12,
        isPositive: true,
      },
    },
    {
      id: "total-value",
      title: "Total Value",
      value: statistics?.totalValue
        ? `$${statistics.totalValue.toLocaleString()}`
        : "$0",
      description: "Combined contract value",
      icon: <DollarSign size={16} className="text-orange-500" />,
    },
    {
      id: "avg-duration",
      title: "Avg Duration",
      value: statistics?.averageDuration || "0 days",
      description: "Average contract duration",
      icon: <Calendar size={16} className="text-purple-500" />,
    },
  ];

  // Context menu actions for advanced layout
  const contextMenuActions = [
    {
      id: "open",
      label: "Open",
      icon: <LottieIconPlayer icon={LottieIconLib.view} size={16} />,
      onClick: (item: Contract) => handleOpenContract(item),
    },
    {
      id: "edit",
      label: "Edit",
      icon: <LottieIconPlayer icon={LottieIconLib.edit} size={16} />,
      onClick: (item: Contract) => handleEditContractDialog(item),
    },
    {
      id: "separator1",
      label: "",
      separator: true,
      onClick: () => {},
    },
    {
      id: "delete",
      label: "Delete",
      icon: (
        <LottieIconPlayer
          icon={LottieIconLib.delete}
          size={16}
          className="text-red-500"
        />
      ),
      onClick: (item: Contract) => handleDeleteContractDialog(item),
    },
  ];

  // Table columns configuration
  const columns = [
    {
      key: "title",
      label: "Contract",
      dataType: "document" as DataType,
    },
    {
      key: "clientName",
      label: "Client",
    },
    {
      key: "status",
      label: "Status",
    },
    {
      key: "contractValue",
      label: "Value",
      render: (item: Contract) => `$${item.contractValue.toLocaleString()}`,
    },
    {
      key: "startDate",
      label: "Start Date",
      render: (item: Contract) => new Date(item.startDate).toLocaleDateString(),
    },
    {
      key: "endDate",
      label: "End Date",
      render: (item: Contract) => new Date(item.endDate).toLocaleDateString(),
    },
  ];

  // Categories for filtering based on contract status
  const categories = [
    {
      key: "active",
      label: "Active",
      count: contracts.filter((c) => c.status === "active").length,
    },
    {
      key: "draft",
      label: "Draft",
      count: contracts.filter((c) => c.status === "draft").length,
    },
    {
      key: "completed",
      label: "Completed",
      count: contracts.filter((c) => c.status === "completed").length,
    },
    {
      key: "terminated",
      label: "Terminated",
      count: contracts.filter((c) => c.status === "terminated").length,
    },
    {
      key: "expired",
      label: "Expired",
      count: contracts.filter((c) => c.status === "expired").length,
    },
  ];

  return (
    <CanRead entity={DEFAULT_ENTITIES.CONTRACT} resourceId={personalizedRoute}>
      <Listing
        className="space-y-1"
        toolbar={
          isAdvanced
            ? {
                searchValue: searchTerm,
                onSearchChange: setSearchTerm,
                onSearchCommit: handleSearchCommit,
                searchPlaceholder: "Search contracts...",
                quickFilters,
                viewMode: advancedViewMode,
                onViewModeChange: setAdvancedViewMode,
                enabledViewModes: ["table", "grid", "flow"],
                showInsightsButton: true,
                insightsWidgets,
              }
            : undefined
        }
      >
        {/* Basic Layout Components */}
        {!isAdvanced && (
          <>
            <ContractHeader
              onCreateContract={() => setIsCreateDialogOpen(true)}
              contractsCount={contracts.length}
            />

            <ContractStatistics statistics={statistics} isLoading={isLoading} />

            <Listing.Filters
              searchTerm={searchTerm}
              onSearchChange={handleSearch}
              enableDynamicFilters={true}
              columns={columns.map((col) => ({
                key: col.key,
                label: col.label,
                searchable: true,
                type:
                  col.key === "contractValue"
                    ? "number"
                    : col.key === "startDate" || col.key === "endDate"
                    ? "date"
                    : "string",
              }))}
              tableData={contracts}
              defaultFilterColumn="title"
              autoSelectDefaultColumn={true}
              onRefresh={handleRefresh}
              loading={isTableLoading}
              enableViewModeControls={true}
              viewMode={basicViewMode}
              onViewModeChange={setBasicViewMode}
              enableControls={true}
              controlsViewMode={controlsViewMode}
              onControlsViewModeChange={setControlsViewMode}
              categoryFilter={categoryFilter}
              onCategoryFilterChange={setCategoryFilter}
              categories={categories}
              controlsActions={
                <RBACWrapper
                  entity={DEFAULT_ENTITIES.CONTRACT}
                  action={PERMISSION_ACTIONS.CREATE}
                >
                  <Button
                    variant="secondary"
                    onClick={() => setIsCreateDialogOpen(true)}
                  >
                    <LottieIconPlayer
                      icon={LottieIconLib.add}
                      size={16}
                      className="mr-2"
                    />
                    Create Contract
                  </Button>
                </RBACWrapper>
              }
            />
          </>
        )}

        {/* Advanced Layout Header */}
        {isAdvanced && (
          <Listing.Header
            title="Contracts"
            caption={`Manage your contracts and agreements, ${
              contracts.length
            } ${contracts.length === 1 ? "contract" : "contracts"}`}
            actions={
              <RBACWrapper
                entity={DEFAULT_ENTITIES.CONTRACT}
                action={PERMISSION_ACTIONS.CREATE}
              >
                <Button
                  variant="secondary"
                  onClick={() => setIsCreateDialogOpen(true)}
                >
                  <LottieIconPlayer
                    icon={LottieIconLib.add}
                    size={16}
                    className="mr-2"
                  />
                  Create New Contract
                </Button>
              </RBACWrapper>
            }
          />
        )}

        {/* Advanced Layout - Conditional Rendering */}
        {isAdvanced && advancedViewMode === "flow" && (
          <Listing.Flow
            data={contracts}
            loading={isTableLoading}
            getDataType={(item) => getDataType(item, "title")}
            contextMenuActions={contextMenuActions}
            emptyState={
              <div className="text-center py-12">
                <FileText className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                  No contracts
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Get started by creating your first contract.
                </p>
              </div>
            }
          />
        )}

        {/* Unified Data Renderer - Handles Table/Cards switching globally */}
        {((isAdvanced && advancedViewMode !== "flow") || !isAdvanced) && (
          <Listing.Table
            id="contracts-table"
            data={contracts}
            columns={columns}
            loading={isTableLoading}
            viewMode={basicViewMode}
            displayMode={
              isAdvanced
                ? advancedViewMode === "grid"
                  ? "cards"
                  : "table"
                : controlsViewMode
            }
            getDataType={getDataType}
            renderCard={renderContractCard}
            contextMenuActions={contextMenuActions}
            emptyState={
              <div className="text-center py-12">
                <FileText className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                  No contracts
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Get started by creating your first contract.
                </p>
              </div>
            }
          />
        )}

        {/* Create Contract Dialog */}
        <ContractCreateDialog
          open={isCreateDialogOpen}
          onOpenChange={setIsCreateDialogOpen}
          onCreateContract={handleCreateContract}
          isLoading={isLoading}
        />

        {/* Edit Contract Dialog */}
        {selectedContract && (
          <ContractEditDialog
            contract={selectedContract}
            isOpen={isEditDialogOpen}
            onOpenChange={setIsEditDialogOpen}
            onSubmit={handleEditSubmit}
            isUpdating={isLoading}
          />
        )}

        {/* Delete Contract Dialog */}
        {selectedContract && (
          <ContractDeleteDialog
            contract={selectedContract}
            isOpen={isDeleteDialogOpen}
            onOpenChange={setIsDeleteDialogOpen}
            onConfirm={handleDeleteConfirm}
            isDeleting={isLoading}
          />
        )}
      </Listing>
    </CanRead>
  );
}

// Export compound component with dot notation
export const Contracts = Object.assign(ContractsContainer, {
  Header: ContractHeader,
  Statistics: ContractStatistics,
  Table: ContractTable,
  CreateDialog: ContractCreateDialog,
  EditDialog: ContractEditDialog,
  DeleteDialog: ContractDeleteDialog,
});
